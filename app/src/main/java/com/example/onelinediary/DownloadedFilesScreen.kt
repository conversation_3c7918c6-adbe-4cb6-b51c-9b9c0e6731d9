package com.example.onelinediary

import android.content.Intent
import android.net.Uri
import android.util.Log
import android.webkit.MimeTypeMap
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.content.FileProvider
import com.example.onelinediary.components.standardButtonColors
import com.example.onelinediary.components.standardButtonModifier
import com.example.onelinediary.components.standardButtonPadding
import com.example.onelinediary.components.standardButtonShape
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun DownloadedFilesScreen(
    folderPath: String,
    onBack: () -> Unit
) {
    val context = LocalContext.current

    // State for current directory navigation
    var currentPath by remember { mutableStateOf(folderPath) }

    // Function to open a file or navigate to directory
    fun openFile(file: File) {
        try {
            if (file.isDirectory) {
                // Navigate into the directory
                currentPath = file.absolutePath
                return
            }

            // Get the file URI using FileProvider
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )

            // Get the MIME type
            val mimeType = getMimeType(file.name) ?: "*/*"
            Log.d("DownloadedFilesScreen", "Opening file: ${file.name}, MIME type: $mimeType, URI: $uri")

            // Create intent to open the file
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, mimeType)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            // Check if there's an app that can handle this file type
            val resolveInfo = intent.resolveActivity(context.packageManager)
            Log.d("DownloadedFilesScreen", "Resolve activity result: $resolveInfo")

            if (resolveInfo != null) {
                Log.d("DownloadedFilesScreen", "Starting activity to open file")
                context.startActivity(intent)
            } else {
                Log.w("DownloadedFilesScreen", "No app found to handle MIME type: $mimeType")

                // Try with a generic intent as fallback
                try {
                    val genericIntent = Intent(Intent.ACTION_VIEW).apply {
                        setDataAndType(uri, "*/*")
                        addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }

                    if (genericIntent.resolveActivity(context.packageManager) != null) {
                        Log.d("DownloadedFilesScreen", "Trying generic intent")
                        context.startActivity(Intent.createChooser(genericIntent, "Open with"))
                    } else {
                        Toast.makeText(context, "No app found to open this file type ($mimeType). Try installing a file manager or appropriate app.", Toast.LENGTH_LONG).show()
                    }
                } catch (e: Exception) {
                    Log.e("DownloadedFilesScreen", "Generic intent also failed", e)
                    Toast.makeText(context, "No app found to open this file type ($mimeType). Try installing a file manager or appropriate app.", Toast.LENGTH_LONG).show()
                }
            }
        } catch (e: Exception) {
            Log.e("DownloadedFilesScreen", "Error opening file: ${file.name}", e)
            Toast.makeText(context, "Error opening file: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    // State for files in the folder
    var files by remember { mutableStateOf<List<File>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var errorMessage by remember { mutableStateOf("") }

    // Load files when the current path changes
    LaunchedEffect(currentPath) {
        isLoading = true
        try {
            val folder = File(currentPath)
            if (folder.exists() && folder.isDirectory) {
                files = folder.listFiles()?.toList() ?: emptyList()
                files = files.sortedWith(compareBy<File> { it.isFile }.thenBy { it.name })
                errorMessage = ""
            } else {
                errorMessage = "Folder not found: $currentPath"
                files = emptyList()
            }
        } catch (e: Exception) {
            errorMessage = "Error loading files: ${e.message}"
            files = emptyList()
        } finally {
            isLoading = false
        }
    }

    // Get the background color from the theme
    val backgroundColor = MaterialTheme.colorScheme.background

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .padding(16.dp)
    ) {
        // Header with back button
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(
                onClick = {
                    // If we're in a subdirectory, go back to parent
                    val currentFile = File(currentPath)
                    val parentFile = currentFile.parentFile
                    if (parentFile != null && parentFile.absolutePath.startsWith(folderPath)) {
                        currentPath = parentFile.absolutePath
                    } else {
                        // If we're at the root download folder or outside it, go back to previous screen
                        onBack()
                    }
                },
                modifier = Modifier.size(48.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = MaterialTheme.colorScheme.onBackground
                )
            }

            Spacer(modifier = Modifier.width(8.dp))

            Column {
                Text(
                    text = "Downloaded Files",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = MaterialTheme.colorScheme.onBackground
                )

                // Show current directory if different from root
                if (currentPath != folderPath) {
                    val relativePath = File(currentPath).name
                    Text(
                        text = "📁 $relativePath",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Folder path display
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Location:",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = MaterialTheme.colorScheme.onSurface
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = currentPath,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Content area
        when {
            isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }

            errorMessage.isNotEmpty() -> {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Text(
                        text = errorMessage,
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        modifier = Modifier.padding(16.dp),
                        textAlign = TextAlign.Center
                    )
                }
            }

            files.isEmpty() -> {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "No files found in this folder",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier.padding(16.dp),
                        textAlign = TextAlign.Center
                    )
                }
            }

            else -> {
                // Files list
                Text(
                    text = "Files (${files.size})",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = MaterialTheme.colorScheme.onBackground,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(files) { file ->
                        FileItem(
                            file = file,
                            onFileClick = { openFile(it) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun FileItem(
    file: File,
    onFileClick: (File) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onFileClick(file) },
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // File icon using text
            Text(
                text = if (file.isDirectory) "📁" else "📄",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            // File info
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = file.name,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = MaterialTheme.colorScheme.onSurface
                )

                if (file.isFile) {
                    val fileSize = formatFileSize(file.length())
                    val lastModified = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                        .format(Date(file.lastModified()))

                    Text(
                        text = "$fileSize • $lastModified",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

private fun formatFileSize(bytes: Long): String {
    if (bytes < 1024) return "$bytes B"
    val kb = bytes / 1024.0
    if (kb < 1024) return "%.1f KB".format(kb)
    val mb = kb / 1024.0
    if (mb < 1024) return "%.1f MB".format(mb)
    val gb = mb / 1024.0
    return "%.1f GB".format(gb)
}

private fun getMimeType(fileName: String): String? {
    val extension = fileName.substringAfterLast('.', "")
    return when (extension.lowercase()) {
        "txt" -> "text/plain"
        "jpg", "jpeg" -> "image/jpeg"
        "png" -> "image/png"
        "gif" -> "image/gif"
        "mp4" -> "video/mp4"
        "mp3" -> "audio/mpeg"
        "wav" -> "audio/wav"
        "pdf" -> "application/pdf"
        "doc" -> "application/msword"
        "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        else -> MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension)
    }
}
